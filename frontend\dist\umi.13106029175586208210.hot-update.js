globalThis.makoModuleHotUpdate('src/.umi/umi.ts?hmr', {
    modules: {
        "src/app.tsx": function(module, exports, __mako_require__) {
            "use strict";
            __mako_require__.d(exports, "__esModule", {
                value: true
            });
            function _export(target, all) {
                for(var name in all)Object.defineProperty(target, name, {
                    enumerable: true,
                    get: all[name]
                });
            }
            __mako_require__.e(exports, {
                getInitialState: function() {
                    return getInitialState;
                },
                layout: function() {
                    return layout;
                }
            });
            var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
            var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
            var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
            var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
            var _max = __mako_require__("src/.umi/exports.ts");
            var _components = __mako_require__("src/components/index.ts");
            var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
            var _services = __mako_require__("src/services/index.ts");
            var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
            var prevRefreshReg;
            var prevRefreshSig;
            prevRefreshReg = self.$RefreshReg$;
            prevRefreshSig = self.$RefreshSig$;
            self.$RefreshReg$ = (type, id)=>{
                _reactrefresh.register(type, module.id + id);
            };
            self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
            async function getInitialState() {
                const fetchUserInfo = async ()=>{
                    try {
                        return await _services.UserService.getUserProfile();
                    } catch (error) {
                        console.error('获取用户信息失败:', error);
                        // 如果获取用户信息失败，可能是token过期，清除登录状态
                        _services.AuthService.clearToken();
                        return undefined;
                    }
                };
                const fetchTeamInfo = async ()=>{
                    try {
                        // 只有当Token中包含团队信息时才尝试获取团队详情
                        if (!(0, _tokenUtils.hasTeamInCurrentToken)()) return undefined;
                        return await _services.TeamService.getCurrentTeamDetail();
                    } catch (error) {
                        console.error('获取团队信息失败:', error);
                        return undefined;
                    }
                };
                // 如果不是登录页面，执行
                const { location } = _max.history;
                if (![
                    '/user/login',
                    '/user/register'
                ].includes(location.pathname)) try {
                    // 检查登录状态
                    if (!_services.AuthService.isLoggedIn()) return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    // 获取用户信息
                    const currentUser = await fetchUserInfo();
                    if (!currentUser) return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                    // 尝试获取团队信息（如果Token中包含团队信息）
                    const currentTeam = await fetchTeamInfo();
                    return {
                        fetchUserInfo,
                        fetchTeamInfo,
                        currentUser,
                        currentTeam
                    };
                } catch (error) {
                    console.error('初始化失败:', error);
                    return {
                        fetchUserInfo,
                        fetchTeamInfo
                    };
                }
                return {
                    fetchUserInfo,
                    fetchTeamInfo
                };
            }
            const layout = ({ initialState })=>{
                var _initialState_currentUser;
                return {
                    // 水印
                    waterMarkProps: {
                        content: initialState === null || initialState === void 0 ? void 0 : (_initialState_currentUser = initialState.currentUser) === null || _initialState_currentUser === void 0 ? void 0 : _initialState_currentUser.name
                    },
                    // 底部版权信息
                    footerRender: ()=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.Footer, {}, void 0, false, {
                            fileName: "src/app.tsx",
                            lineNumber: 102,
                            columnNumber: 25
                        }, this),
                    // 移除右侧内容渲染
                    rightContentRender: false,
                    // 页面切换时触发
                    onPageChange: ()=>{
                        const { location } = _max.history;
                        // 如果没有登录，重定向到 login
                        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) && ![
                            '/user/login',
                            '/user/register'
                        ].includes(location.pathname)) _max.history.push('/user/login');
                    },
                    // 自定义布局区域的背景颜色、文字颜色等
                    bgLayoutImgList: [
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
                            left: 85,
                            bottom: 100,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
                            bottom: -68,
                            right: -45,
                            height: '303px'
                        },
                        {
                            src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
                            bottom: 0,
                            left: 0,
                            width: '331px'
                        }
                    ],
                    // 自定义链接
                    links: [],
                    // 自定义菜单头
                    menuHeaderRender: undefined,
                    // 自定义 403 页面
                    // unAccessible: <div>unAccessible</div>,
                    // 增加一个 loading 的状态
                    childrenRender: (children)=>{
                        return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_components.ErrorBoundary, {
                            children: [
                                children,
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                                    fileName: "src/app.tsx",
                                    lineNumber: 149,
                                    columnNumber: 11
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/app.tsx",
                            lineNumber: 146,
                            columnNumber: 9
                        }, this);
                    }
                };
            };
            if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
            if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
            function registerClassComponent(filename, moduleExports) {
                for(const key in moduleExports)try {
                    if (key === "__esModule") continue;
                    const exportValue = moduleExports[key];
                    if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
                } catch (e) {}
            }
            function $RefreshIsReactComponentLike$(moduleExports) {
                if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
                for(var key in moduleExports)try {
                    if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
                } catch (e) {}
                return false;
            }
            registerClassComponent(module.id, module.exports);
            if ($RefreshIsReactComponentLike$(module.exports)) {
                module.meta.hot.accept();
                _reactrefresh.performReactRefresh();
            }
        }
    }
}, function(runtime) {
    runtime._h = '738410732556153861';
    runtime.updateEnsure2Map({
        "src/.umi/core/EmptyRoute.tsx": [
            "src/.umi/core/EmptyRoute.tsx"
        ],
        "src/.umi/plugin-layout/Layout.tsx": [
            "vendors",
            "src/.umi/plugin-layout/Layout.tsx"
        ],
        "src/pages/404.tsx": [
            "p__404"
        ],
        "src/pages/Dashboard/index.tsx": [
            "p__Dashboard__index"
        ],
        "src/pages/help/index.tsx": [
            "p__help__index"
        ],
        "src/pages/invite/[token].tsx": [
            "common",
            "p__invite__token"
        ],
        "src/pages/personal-center/index.tsx": [
            "common",
            "src/pages/personal-center/index.tsx"
        ],
        "src/pages/subscription/index.tsx": [
            "p__subscription__index"
        ],
        "src/pages/team-management/index.tsx": [
            "common",
            "src/pages/team-management/index.tsx"
        ],
        "src/pages/team/detail/index.tsx": [
            "p__team__detail__index"
        ],
        "src/pages/team/index.tsx": [
            "common",
            "p__team__index"
        ],
        "src/pages/user/index.tsx": [
            "p__user__index"
        ],
        "src/pages/user/invitations/index.tsx": [
            "common",
            "p__user__invitations__index"
        ],
        "src/pages/user/login/index.tsx": [
            "common",
            "p__user__login__index"
        ]
    });
    ;
});

//# sourceMappingURL=umi.13106029175586208210.hot-update.js.map